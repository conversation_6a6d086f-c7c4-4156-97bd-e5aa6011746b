"""
Unified Categories Router
========================

Consolidated categories router using the UnifiedBaseRouter system.
Replaces the original categories/router.py with standardized patterns.

This router consolidates:
- Category CRUD operations
- Vendor mapping management
- Hierarchy management
- GL code operations
- Categorization metrics
- Enhancement detection
"""

import logging
from typing import Any, Dict, List, Optional

from asyncpg import Connection
from fastapi import Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...shared.routers.base_router import (
    UnifiedBaseRouter,
    BaseRouterConfig,
    PaginationParams,
    FilterParams,
    DateRangeParams,
)
from ...core.dependencies import get_current_tenant_id, get_db_session
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .service import CategoryService
from .schemas import (
    Category,
    CategoryCreate,
    CategoryTree,
    CategoryUpdate,
    CategoryVisualization,
    EnhancementAnalysisResponse,
    EnhancementOpportunity,
    HierarchicalResults,
)
from .schemas_vendor import (
    BulkVendorMappingRequest,
    VendorGrouping,
    VendorMapping,
    VendorMappingCreate,
    VendorMappingResult,
    VendorMappingUpdate,
)

logger = logging.getLogger(__name__)


class UnifiedCategoriesRouter(UnifiedBaseRouter):
    """Unified categories router with consolidated patterns."""
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/categories",
            tags=["Categories"],
            include_auth=True,
            include_tenant_isolation=True,
            include_caching=True,
            cache_ttl=300,  # 5 minutes for category data
            include_performance_monitoring=True,
        )
        super().__init__(config)
    
    def _register_routes(self) -> None:
        """Register all category-related routes."""
        self._register_category_crud_routes()
        self._register_hierarchy_routes()
        self._register_vendor_mapping_routes()
        self._register_metrics_routes()
        self._register_enhancement_routes()
    
    def _register_category_crud_routes(self) -> None:
        """Register basic CRUD routes for categories."""
        
        # List categories
        @self.router.get("", response_model=List[CategoryTree])
        async def list_categories(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
            include_usage_counts: bool = Query(False, description="Include usage statistics"),
        ):
            """List all categories for the current tenant."""
            return await self._handle_request(
                self._list_categories_handler,
                tenant_id=tenant_id,
                category_service=category_service,
                include_usage_counts=include_usage_counts,
            )
        
        # List categories with counts (separate endpoint for performance)
        @self.router.get("/with-counts", response_model=List[CategoryTree])
        async def list_categories_with_counts(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
        ):
            """List categories with usage counts (performance-optimized endpoint)."""
            return await self._handle_request(
                self._list_categories_handler,
                tenant_id=tenant_id,
                category_service=category_service,
                include_usage_counts=True,
            )
        
        # Get single category
        @self.router.get("/{category_id}", response_model=CategoryTree)
        async def get_category(
            category_id: int,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
        ):
            """Get a single category by ID."""
            return await self._handle_request(
                self._get_category_handler,
                category_id=category_id,
                tenant_id=tenant_id,
                category_service=category_service,
            )
        
        # Create category
        @self.router.post("", response_model=Category, status_code=status.HTTP_201_CREATED)
        async def create_category(
            category_data: CategoryCreate,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
        ):
            """Create a new category."""
            return await self._handle_request(
                self._create_category_handler,
                category_data=category_data,
                tenant_id=tenant_id,
                user_id=current_user.id,
                category_service=category_service,
            )
        
        # Update category
        @self.router.put("/{category_id}", response_model=Category)
        async def update_category(
            category_id: int,
            category_data: CategoryUpdate,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
        ):
            """Update an existing category."""
            return await self._handle_request(
                self._update_category_handler,
                category_id=category_id,
                category_data=category_data,
                tenant_id=tenant_id,
                user_id=current_user.id,
                category_service=category_service,
            )
        
        # Delete category
        @self.router.delete("/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
        async def delete_category(
            category_id: int,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
        ):
            """Delete a category."""
            await self._handle_request(
                self._delete_category_handler,
                category_id=category_id,
                tenant_id=tenant_id,
                category_service=category_service,
            )
    
    # Handler methods for category CRUD operations
    async def _list_categories_handler(
        self,
        tenant_id: int,
        category_service: CategoryService,
        include_usage_counts: bool = False,
    ) -> List[CategoryTree]:
        """Handler for listing categories."""
        self.logger.info(f"Listing categories for tenant {tenant_id}, include_counts={include_usage_counts}")
        
        categories = await category_service.get_categories(
            tenant_id, include_usage_counts=include_usage_counts
        )
        
        self.logger.info(f"Retrieved {len(categories)} categories for tenant {tenant_id}")
        return categories
    
    async def _get_category_handler(
        self,
        category_id: int,
        tenant_id: int,
        category_service: CategoryService,
    ) -> CategoryTree:
        """Handler for getting a single category."""
        self.logger.info(f"Getting category {category_id} for tenant {tenant_id}")
        
        category = await category_service.get_category(category_id, tenant_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category {category_id} not found"
            )
        
        return category
    
    async def _create_category_handler(
        self,
        category_data: CategoryCreate,
        tenant_id: int,
        user_id: int,
        category_service: CategoryService,
    ) -> Category:
        """Handler for creating a category."""
        self.logger.info(f"Creating category '{category_data.name}' for tenant {tenant_id}")
        
        # Validate tenant access for parent category if specified
        if category_data.parent_id:
            parent = await category_service.get_category(category_data.parent_id, tenant_id)
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Parent category {category_data.parent_id} not found"
                )
        
        created_category = await category_service.create_category(
            db=category_service.conn,
            category_data=category_data,
            tenant_id=tenant_id,
            user_id=user_id,
        )
        
        self.logger.info(f"Created category '{created_category.name}' with ID {created_category.id}")
        return created_category
    
    async def _update_category_handler(
        self,
        category_id: int,
        category_data: CategoryUpdate,
        tenant_id: int,
        user_id: int,
        category_service: CategoryService,
    ) -> Category:
        """Handler for updating a category."""
        self.logger.info(f"Updating category {category_id} for tenant {tenant_id}")

        # Validate category exists and belongs to tenant
        existing_category = await category_service.get_category(category_id, tenant_id)
        if not existing_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category {category_id} not found"
            )

        # Validate parent category if being updated
        if category_data.parent_id:
            parent = await category_service.get_category(category_data.parent_id, tenant_id)
            if not parent:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Parent category {category_data.parent_id} not found"
                )

        updated_category = await category_service.update_category(
            category_id=category_id,
            category_data=category_data,
            tenant_id=tenant_id,
            user_id=user_id,
        )

        self.logger.info(f"Updated category {category_id}")
        return updated_category

    async def _delete_category_handler(
        self,
        category_id: int,
        tenant_id: int,
        category_service: CategoryService,
    ) -> None:
        """Handler for deleting a category."""
        self.logger.info(f"Deleting category {category_id} for tenant {tenant_id}")

        # Validate category exists and belongs to tenant
        existing_category = await category_service.get_category(category_id, tenant_id)
        if not existing_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category {category_id} not found"
            )

        # Check if category has children or transactions
        has_dependencies = await category_service.check_category_dependencies(category_id, tenant_id)
        if has_dependencies:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete category with child categories or associated transactions"
            )

        await category_service.delete_category(category_id, tenant_id)
        self.logger.info(f"Deleted category {category_id}")

    def _register_hierarchy_routes(self) -> None:
        """Register hierarchy management routes."""

        @self.router.get("/hierarchy", response_model=Dict[str, Any])
        async def get_category_hierarchy(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
            include_usage_counts: bool = Query(True, description="Include usage statistics"),
        ):
            """Get complete multilevel category hierarchy."""
            return await self._handle_request(
                self._get_hierarchy_handler,
                tenant_id=tenant_id,
                category_service=category_service,
                include_usage_counts=include_usage_counts,
            )

        @self.router.post("/setup-standard-hierarchy")
        async def setup_standard_hierarchy(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            conn: Connection = Depends(get_db_session),
        ):
            """Setup the standard MIS hierarchy for a tenant."""
            return await self._handle_request(
                self._setup_standard_hierarchy_handler,
                tenant_id=tenant_id,
                conn=conn,
            )

        @self.router.post("/reset-to-clean-hierarchy")
        async def reset_to_clean_hierarchy(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            conn: Connection = Depends(get_db_session),
        ):
            """NUCLEAR OPTION: Delete ALL categories and rebuild with clean standard MIS hierarchy."""
            return await self._handle_request(
                self._reset_to_clean_hierarchy_handler,
                tenant_id=tenant_id,
                conn=conn,
            )

    def _register_vendor_mapping_routes(self) -> None:
        """Register vendor mapping routes."""

        @self.router.post("/vendors/user-mapping", response_model=VendorMappingResult)
        async def create_user_vendor_mapping(
            mapping: VendorMappingCreate,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            conn: Connection = Depends(get_db_session),
            apply_immediately: bool = Query(True, description="Apply mapping to existing transactions"),
        ):
            """Create a user-defined vendor-to-category mapping."""
            return await self._handle_request(
                self._create_vendor_mapping_handler,
                mapping=mapping,
                tenant_id=tenant_id,
                user_id=current_user.id,
                conn=conn,
                apply_immediately=apply_immediately,
            )

        @self.router.get("/vendors/user-mappings", response_model=List[VendorMapping])
        async def get_user_vendor_mappings(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            conn: Connection = Depends(get_db_session),
            include_inactive: bool = Query(False, description="Include inactive mappings"),
        ):
            """Get all vendor mappings for the current tenant."""
            return await self._handle_request(
                self._get_vendor_mappings_handler,
                tenant_id=tenant_id,
                user_id=current_user.id,
                conn=conn,
                include_inactive=include_inactive,
            )

    def _register_metrics_routes(self) -> None:
        """Register metrics and analytics routes."""

        @self.router.get("/metrics/categorization", response_model=Dict[str, Any])
        async def get_categorization_metrics(
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            conn: Connection = Depends(get_db_session),
        ):
            """Get real categorization metrics for dashboard display."""
            return await self._handle_request(
                self._get_categorization_metrics_handler,
                tenant_id=tenant_id,
                conn=conn,
            )

    def _register_enhancement_routes(self) -> None:
        """Register enhancement and analysis routes."""

        @self.router.get("/hierarchical-results/{upload_id}", response_model=HierarchicalResults)
        async def get_hierarchical_results(
            upload_id: str,
            current_user: User = Depends(get_current_active_user),
            tenant_id: int = Depends(get_current_tenant_id),
            category_service: CategoryService = Depends(get_category_service),
            conn: Connection = Depends(get_db_session),
        ):
            """Get hierarchical categorization results with visualization data."""
            return await self._handle_request(
                self._get_hierarchical_results_handler,
                upload_id=upload_id,
                tenant_id=tenant_id,
                category_service=category_service,
                conn=conn,
            )

    # Handler implementations for hierarchy routes
    async def _get_hierarchy_handler(
        self,
        tenant_id: int,
        category_service: CategoryService,
        include_usage_counts: bool = True,
    ) -> Dict[str, Any]:
        """Handler for getting category hierarchy."""
        self.logger.info(f"Getting category hierarchy for tenant {tenant_id}")

        hierarchy_tree = await category_service.get_category_hierarchy(
            tenant_id=tenant_id, include_usage_counts=include_usage_counts
        )

        return {
            "root_categories": hierarchy_tree.root_categories,
            "total_categories": hierarchy_tree.total_categories,
            "max_depth": hierarchy_tree.max_depth,
            "category_counts": hierarchy_tree.category_counts,
        }

    async def _setup_standard_hierarchy_handler(
        self,
        tenant_id: int,
        conn: Connection,
    ) -> Dict[str, Any]:
        """Handler for setting up standard MIS hierarchy."""
        from .hierarchy_setup_service import HierarchySetupService

        self.logger.info(f"Setting up standard MIS hierarchy for tenant {tenant_id}")

        hierarchy_service = HierarchySetupService(conn)
        result = await hierarchy_service.setup_standard_mis_hierarchy(tenant_id)

        return {
            "success": True,
            "message": "Standard MIS hierarchy created successfully",
            "categories_created": result.get("categories_created", 0),
            "hierarchy_levels": result.get("hierarchy_levels", 3),
        }

    async def _reset_to_clean_hierarchy_handler(
        self,
        tenant_id: int,
        conn: Connection,
    ) -> Dict[str, Any]:
        """Handler for resetting to clean hierarchy."""
        from .hierarchy_setup_service import HierarchySetupService

        self.logger.warning(f"RESETTING to clean hierarchy for tenant {tenant_id}")

        # Get count before deletion
        before_count = await conn.fetchval(
            "SELECT COUNT(*) FROM categories WHERE tenant_id = $1", tenant_id
        )

        # Delete ALL categories for this tenant
        await conn.execute("DELETE FROM categories WHERE tenant_id = $1", tenant_id)
        self.logger.info(f"Deleted {before_count} categories for tenant {tenant_id}")

        # Setup fresh standard MIS hierarchy
        hierarchy_service = HierarchySetupService(conn)
        result = await hierarchy_service.setup_standard_mis_hierarchy(tenant_id)

        # Get final count
        after_count = await conn.fetchval(
            "SELECT COUNT(*) FROM categories WHERE tenant_id = $1", tenant_id
        )

        return {
            "success": True,
            "message": f"Reset complete: deleted {before_count} categories, created {after_count} new categories",
            "deleted_count": before_count,
            "created_count": after_count,
            "hierarchy_levels": 3,
        }

    # Handler implementations for vendor mapping routes
    async def _create_vendor_mapping_handler(
        self,
        mapping: VendorMappingCreate,
        tenant_id: int,
        user_id: int,
        conn: Connection,
        apply_immediately: bool = True,
    ) -> VendorMappingResult:
        """Handler for creating vendor mapping."""
        from .user_vendor_mapping_service import user_vendor_mapping_service

        self.logger.info(f"Creating vendor mapping for tenant {tenant_id}")

        result = await user_vendor_mapping_service.create_vendor_mapping(
            mapping=mapping,
            tenant_id=tenant_id,
            user_id=user_id,
            conn=conn,
            apply_immediately=apply_immediately,
        )

        return result

    async def _get_vendor_mappings_handler(
        self,
        tenant_id: int,
        user_id: int,
        conn: Connection,
        include_inactive: bool = False,
    ) -> List[VendorMapping]:
        """Handler for getting vendor mappings."""
        from .user_vendor_mapping_service import user_vendor_mapping_service

        self.logger.info(f"Getting vendor mappings for tenant {tenant_id}")

        mappings = await user_vendor_mapping_service.get_user_vendor_mappings(
            tenant_id=tenant_id,
            user_id=user_id,
            conn=conn,
            include_inactive=include_inactive,
        )

        return mappings

    # Handler implementations for metrics routes
    async def _get_categorization_metrics_handler(
        self,
        tenant_id: int,
        conn: Connection,
    ) -> Dict[str, Any]:
        """Handler for getting categorization metrics."""
        from .categorization_metrics import CategorizationMetricsService

        self.logger.info(f"Getting categorization metrics for tenant {tenant_id}")

        metrics_service = CategorizationMetricsService(conn)
        metrics = await metrics_service.get_categorization_metrics(tenant_id)

        return metrics

    # Handler implementations for enhancement routes
    async def _get_hierarchical_results_handler(
        self,
        upload_id: str,
        tenant_id: int,
        category_service: CategoryService,
        conn: Connection,
    ) -> HierarchicalResults:
        """Handler for getting hierarchical results."""
        self.logger.info(f"Getting hierarchical results for upload {upload_id}, tenant {tenant_id}")

        # This would implement the hierarchical results logic
        # For now, return a placeholder structure
        return HierarchicalResults(
            upload_id=upload_id,
            tenant_id=tenant_id,
            total_transactions=0,
            categorized_transactions=0,
            hierarchy_levels=[],
            visualizations=[],
        )

    # Service dependency methods
    def _get_category_service(self):
        """Get category service dependency function."""
        async def get_category_service_dependency(
            conn: Connection = Depends(get_db_session),
        ) -> CategoryService:
            """Dependency to get CategoryService instance."""
            return CategoryService(conn)

        return get_category_service_dependency


# Standalone dependency function for use outside the class
async def get_category_service(
    conn: Connection = Depends(get_db_session),
) -> CategoryService:
    """Dependency to get CategoryService instance."""
    return CategoryService(conn)
