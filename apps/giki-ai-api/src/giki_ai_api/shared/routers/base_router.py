"""
Unified Base Router System
==========================

Consolidated router system that merges common patterns from:
- domains/auth/secure_router.py
- domains/categories/router.py
- domains/transactions/router.py
- domains/dashboard/router.py
- domains/reports/router.py
- domains/accuracy/router.py

This system provides:
- Standardized router initialization
- Common middleware and error handling
- Consistent authentication patterns
- Unified response formatting
- Common pagination and filtering
- Standardized caching patterns
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type, Union

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...core.database import get_db_session
from ...core.dependencies import get_current_tenant_id
from ...domains.auth.secure_auth import get_current_active_user
from ...shared.error_management.unified_error_handler import (
    UnifiedErrorHandler,
    <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rror<PERSON>everity,
)
from ...shared.services.base_service import BaseService


class BaseRouterConfig(BaseModel):
    """Configuration for base routers."""
    prefix: str
    tags: List[str]
    include_auth: bool = True
    include_tenant_isolation: bool = True
    include_caching: bool = False
    cache_ttl: int = 300  # 5 minutes default
    include_rate_limiting: bool = False
    rate_limit_per_minute: int = 60
    include_performance_monitoring: bool = True


class PaginationParams(BaseModel):
    """Standard pagination parameters."""
    page: int = Query(1, ge=1, description="Page number")
    per_page: int = Query(20, ge=1, le=100, description="Items per page")
    cursor: Optional[str] = Query(None, description="Cursor for pagination")


class DateRangeParams(BaseModel):
    """Standard date range parameters."""
    start_date: Optional[str] = Query(None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Query(None, description="End date (YYYY-MM-DD)")


class FilterParams(BaseModel):
    """Base filter parameters."""
    search: Optional[str] = Query(None, description="Search query")
    sort_by: Optional[str] = Query(None, description="Sort field")
    sort_order: Optional[str] = Query("asc", regex="^(asc|desc)$", description="Sort order")


class StandardResponse(BaseModel):
    """Standard API response format."""
    success: bool = True
    data: Any = None
    message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime = datetime.now()


class PaginatedResponse(StandardResponse):
    """Standard paginated response format."""
    pagination: Dict[str, Any] = {}


class UnifiedBaseRouter(ABC):
    """
    Unified base router providing common functionality for all domain routers.
    
    This class consolidates:
    - Router initialization and configuration
    - Common middleware patterns
    - Authentication and authorization
    - Error handling and response formatting
    - Caching and performance monitoring
    - Pagination and filtering
    """
    
    def __init__(self, config: BaseRouterConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize router with common configuration
        self.router = APIRouter(
            prefix=config.prefix,
            tags=config.tags,
            responses=self._get_standard_responses()
        )
        
        # Setup common dependencies
        self._setup_dependencies()
        
        # Register routes
        self._register_routes()
        
        self.logger.info(f"Initialized {self.__class__.__name__} with prefix {config.prefix}")
    
    def _get_standard_responses(self) -> Dict[int, Dict[str, str]]:
        """Get standard HTTP response definitions."""
        responses = {
            status.HTTP_500_INTERNAL_SERVER_ERROR: {"description": "Internal server error"},
        }
        
        if self.config.include_auth:
            responses.update({
                status.HTTP_401_UNAUTHORIZED: {"description": "Not authenticated"},
                status.HTTP_403_FORBIDDEN: {"description": "Not authorized"},
            })
        
        responses[status.HTTP_404_NOT_FOUND] = {"description": "Not found"}
        responses[status.HTTP_422_UNPROCESSABLE_ENTITY] = {"description": "Validation error"}
        
        return responses
    
    def _setup_dependencies(self) -> None:
        """Setup common dependencies for the router."""
        # Common dependencies that all routes will use
        self.common_deps = []
        
        if self.config.include_auth:
            self.common_deps.extend([
                Depends(get_current_active_user),
            ])
        
        if self.config.include_tenant_isolation:
            self.common_deps.extend([
                Depends(get_current_tenant_id),
                Depends(get_db_session),
            ])
    
    @abstractmethod
    def _register_routes(self) -> None:
        """Register domain-specific routes. Must be implemented by subclasses."""
        pass
    
    # Common route patterns
    def add_list_route(
        self,
        path: str,
        response_model: Type[BaseModel],
        handler: callable,
        include_pagination: bool = True,
        include_filtering: bool = True,
        include_date_range: bool = False,
        cache_enabled: bool = None,
    ) -> None:
        """Add a standardized list route."""
        deps = self.common_deps.copy()
        
        if include_pagination:
            deps.append(Depends(PaginationParams))
        if include_filtering:
            deps.append(Depends(FilterParams))
        if include_date_range:
            deps.append(Depends(DateRangeParams))
        
        @self.router.get(path, response_model=response_model)
        async def list_endpoint(*args, **kwargs):
            try:
                return await self._handle_request(handler, *args, **kwargs)
            except Exception as e:
                error_handler = UnifiedErrorHandler()
                return error_handler.handle_error(e)
        
        # Apply caching if enabled
        if cache_enabled or (cache_enabled is None and self.config.include_caching):
            list_endpoint = self._apply_caching(list_endpoint, path)
    
    def add_get_route(
        self,
        path: str,
        response_model: Type[BaseModel],
        handler: callable,
        cache_enabled: bool = None,
    ) -> None:
        """Add a standardized get route."""
        @self.router.get(path, response_model=response_model)
        async def get_endpoint(*args, **kwargs):
            try:
                return await self._handle_request(handler, *args, **kwargs)
            except Exception as e:
                error_handler = UnifiedErrorHandler()
                return error_handler.handle_error(e)
        
        # Apply caching if enabled
        if cache_enabled or (cache_enabled is None and self.config.include_caching):
            get_endpoint = self._apply_caching(get_endpoint, path)
    
    def add_create_route(
        self,
        path: str,
        request_model: Type[BaseModel],
        response_model: Type[BaseModel],
        handler: callable,
    ) -> None:
        """Add a standardized create route."""
        @self.router.post(path, response_model=response_model, status_code=status.HTTP_201_CREATED)
        async def create_endpoint(request_data: request_model, *args, **kwargs):
            try:
                result = await self._handle_request(handler, request_data, *args, **kwargs)
                return self._format_success_response(result, "Resource created successfully")
            except Exception as e:
                error_handler = UnifiedErrorHandler()
                return error_handler.handle_error(e)
    
    def add_update_route(
        self,
        path: str,
        request_model: Type[BaseModel],
        response_model: Type[BaseModel],
        handler: callable,
    ) -> None:
        """Add a standardized update route."""
        @self.router.put(path, response_model=response_model)
        async def update_endpoint(request_data: request_model, *args, **kwargs):
            try:
                result = await self._handle_request(handler, request_data, *args, **kwargs)
                return self._format_success_response(result, "Resource updated successfully")
            except Exception as e:
                error_handler = UnifiedErrorHandler()
                return error_handler.handle_error(e)
    
    def add_delete_route(
        self,
        path: str,
        handler: callable,
    ) -> None:
        """Add a standardized delete route."""
        @self.router.delete(path, status_code=status.HTTP_204_NO_CONTENT)
        async def delete_endpoint(*args, **kwargs):
            try:
                await self._handle_request(handler, *args, **kwargs)
                return None
            except Exception as e:
                error_handler = UnifiedErrorHandler()
                return error_handler.handle_error(e)
    
    # Helper methods
    async def _handle_request(self, handler: callable, *args, **kwargs) -> Any:
        """Handle request with common error handling and logging."""
        start_time = datetime.now()
        
        try:
            # Log request
            self.logger.info(f"Processing request: {handler.__name__}")
            
            # Execute handler
            result = await handler(*args, **kwargs)
            
            # Log success
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.logger.info(f"Request completed: {handler.__name__} ({processing_time:.2f}ms)")
            
            return result
            
        except Exception as e:
            # Log error
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.logger.error(f"Request failed: {handler.__name__} ({processing_time:.2f}ms) - {str(e)}")
            raise
    
    def _format_success_response(self, data: Any, message: str = None) -> StandardResponse:
        """Format a successful response."""
        return StandardResponse(
            success=True,
            data=data,
            message=message,
            timestamp=datetime.now()
        )
    
    def _format_paginated_response(
        self,
        data: List[Any],
        pagination_params: PaginationParams,
        total_count: int
    ) -> PaginatedResponse:
        """Format a paginated response."""
        total_pages = (total_count + pagination_params.per_page - 1) // pagination_params.per_page
        
        return PaginatedResponse(
            success=True,
            data=data,
            pagination={
                "page": pagination_params.page,
                "per_page": pagination_params.per_page,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": pagination_params.page < total_pages,
                "has_prev": pagination_params.page > 1,
            },
            timestamp=datetime.now()
        )
    
    def _apply_caching(self, endpoint: callable, cache_key: str) -> callable:
        """Apply caching to an endpoint."""
        # This would integrate with the caching middleware
        # For now, return the endpoint unchanged
        return endpoint
    
    def _validate_tenant_access(self, tenant_id: int, resource_tenant_id: int) -> None:
        """Validate tenant access for resources."""
        if tenant_id != resource_tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: insufficient tenant permissions"
            )
    
    def _parse_date_range(self, date_params: DateRangeParams) -> tuple[datetime, datetime]:
        """Parse and validate date range parameters."""
        end_date = datetime.now().date()
        if date_params.end_date:
            end_date = datetime.fromisoformat(date_params.end_date).date()
        
        start_date = end_date - timedelta(days=30)
        if date_params.start_date:
            start_date = datetime.fromisoformat(date_params.start_date).date()
        
        return start_date, end_date
    
    def get_router(self) -> APIRouter:
        """Get the configured FastAPI router."""
        return self.router


# Factory for creating domain-specific routers
class RouterFactory:
    """Factory for creating domain-specific routers."""
    
    @staticmethod
    def create_auth_router() -> APIRouter:
        """Create authentication router."""
        from ...domains.auth.unified_auth_router import UnifiedAuthRouter
        router_instance = UnifiedAuthRouter()
        return router_instance.get_router()
    
    @staticmethod
    def create_categories_router() -> APIRouter:
        """Create categories router."""
        from ...domains.categories.unified_categories_router import UnifiedCategoriesRouter
        router_instance = UnifiedCategoriesRouter()
        return router_instance.get_router()
    
    @staticmethod
    def create_transactions_router() -> APIRouter:
        """Create transactions router."""
        from ...domains.transactions.unified_transactions_router import UnifiedTransactionsRouter
        router_instance = UnifiedTransactionsRouter()
        return router_instance.get_router()
    
    @staticmethod
    def create_dashboard_router() -> APIRouter:
        """Create dashboard router."""
        from ...domains.dashboard.unified_dashboard_router import UnifiedDashboardRouter
        router_instance = UnifiedDashboardRouter()
        return router_instance.get_router()
    
    @staticmethod
    def create_reports_router() -> APIRouter:
        """Create reports router."""
        from ...domains.reports.unified_reports_router import UnifiedReportsRouter
        router_instance = UnifiedReportsRouter()
        return router_instance.get_router()
    
    @staticmethod
    def create_accuracy_router() -> APIRouter:
        """Create accuracy router."""
        from ...domains.accuracy.unified_accuracy_router import UnifiedAccuracyRouter
        router_instance = UnifiedAccuracyRouter()
        return router_instance.get_router()
